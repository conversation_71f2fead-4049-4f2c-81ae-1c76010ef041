from ctypes import windll, c_int, c_int32, c_voidp, c_void_p, WINFUNCTYPE, wintypes, byref, Structure
from threading import get_ident
from signal import signal, SIGINT

WH_KEYBOARD_LL = 13
WM_KEYDOWN = 0x0100
WM_KEYUP = 0x0101
WM_SYSKEYDOWN = 0x0104
WM_SYSKEYUP = 0x0105
WM_QUIT = 0x0012


class KBDLLHOOKSTRUCT(Structure):
    _fields_ = [
        ('wVk', wintypes.WORD),
        ('wScan', wintypes.WORD),
        ('dwFlags', wintypes.DWORD),
        ('time', wintypes.DWORD),
        ('dwExtraInfo', c_void_p)
    ]


VK_CODE_MAP = {
    0x08: 'Backspace',
    0x09: 'Tab',
    0x0D: 'Enter',
    0x10: 'Shift',
    0x11: 'Ctrl',
    0x12: 'Alt',
    0x13: 'Pause',
    0x14: 'Caps Lock',
    0x1B: 'Escape',
    0x20: 'Space',
    0x21: 'Page Up',
    0x22: 'Page Down',
    0x23: 'End',
    0x24: 'Home',
    0x25: 'Left Arrow',
    0x26: 'Up Arrow',
    0x27: 'Right Arrow',
    0x28: 'Down Arrow',
    0x2C: 'Print Screen',
    0x2D: 'Insert',
    0x2E: 'Delete',
    0x30: '0', 0x31: '1', 0x32: '2', 0x33: '3', 0x34: '4',
    0x35: '5', 0x36: '6', 0x37: '7', 0x38: '8', 0x39: '9',
    0x41: 'A', 0x42: 'B', 0x43: 'C', 0x44: 'D', 0x45: 'E',
    0x46: 'F', 0x47: 'G', 0x48: 'H', 0x49: 'I', 0x4A: 'J',
    0x4B: 'K', 0x4C: 'L', 0x4D: 'M', 0x4E: 'N', 0x4F: 'O',
    0x50: 'P', 0x51: 'Q', 0x52: 'R', 0x53: 'S', 0x54: 'T',
    0x55: 'U', 0x56: 'V', 0x57: 'W', 0x58: 'X', 0x59: 'Y',
    0x5A: 'Z',
    0x5B: 'Left Windows',
    0x5C: 'Right Windows',
    0x5D: 'Menu',
    0x60: 'Numpad 0', 0x61: 'Numpad 1', 0x62: 'Numpad 2', 0x63: 'Numpad 3',
    0x64: 'Numpad 4', 0x65: 'Numpad 5', 0x66: 'Numpad 6', 0x67: 'Numpad 7',
    0x68: 'Numpad 8', 0x69: 'Numpad 9',
    0x6A: 'Numpad *', 0x6B: 'Numpad +', 0x6D: 'Numpad -',
    0x6E: 'Numpad .', 0x6F: 'Numpad /',
    0x70: 'F1', 0x71: 'F2', 0x72: 'F3', 0x73: 'F4', 0x74: 'F5',
    0x75: 'F6', 0x76: 'F7', 0x77: 'F8', 0x78: 'F9', 0x79: 'F10',
    0x7A: 'F11', 0x7B: 'F12',
    0x90: 'Num Lock',
    0x91: 'Scroll Lock',
    0xA0: 'Left Shift', 0xA1: 'Right Shift',
    0xA2: 'Left Ctrl', 0xA3: 'Right Ctrl',
    0xA4: 'Left Alt', 0xA5: 'Right Alt',
    0xBA: ';', 0xBB: '=', 0xBC: ',', 0xBD: '-', 0xBE: '.', 0xBF: '/',
    0xC0: '`', 0xDB: '[', 0xDC: '\\', 0xDD: ']', 0xDE: '\''
}

_HOOKPROC = WINFUNCTYPE(
    wintypes.LPARAM,
    c_int32,
    wintypes.WPARAM,
    wintypes.LPARAM
)
_SetWindowsHookEx = windll.user32.SetWindowsHookExW
_SetWindowsHookEx.argtypes = (
    c_int,
    _HOOKPROC,
    wintypes.HINSTANCE,
    wintypes.DWORD
)
_UnhookWindowsHookEx = windll.user32.UnhookWindowsHookEx
_UnhookWindowsHookEx.argtypes = (
    wintypes.HHOOK,
)
_CallNextHookEx = windll.user32.CallNextHookEx
_CallNextHookEx.argtypes = (
    wintypes.HHOOK,
    c_int,
    wintypes.WPARAM,
    wintypes.LPARAM
)
_GetMessage = windll.user32.GetMessageW
_GetMessage.argtypes = (
    c_voidp,
    wintypes.HWND,
    wintypes.UINT,
    wintypes.UINT
)
_PostThreadMessage = windll.user32.PostThreadMessageW
_PostThreadMessage.argtypes = (
    wintypes.DWORD,
    wintypes.UINT,
    wintypes.WPARAM,
    wintypes.LPARAM
)


@_HOOKPROC
def keyboard_hook(nCode, wParam, lParam):
    if nCode >= 0:
        kbd_struct = KBDLLHOOKSTRUCT.from_address(lParam)
        vk_code = kbd_struct.wVk
        key_name = VK_CODE_MAP.get(vk_code, f'Unknown(0x{vk_code:02X})')

        if wParam in (WM_KEYDOWN, WM_SYSKEYDOWN):
            print(f'Key down: {key_name}')
        elif wParam in (WM_KEYUP, WM_SYSKEYUP):
            print(f'Key up: {key_name}')

    return _CallNextHookEx(0, nCode, wParam, lParam)


def signal_handler(signum, _):
    if signum == SIGINT:
        _PostThreadMessage(get_ident(), WM_QUIT, 0, 0)


if __name__ == '__main__':
    signal(SIGINT, signal_handler)
    hook = _SetWindowsHookEx(WH_KEYBOARD_LL, keyboard_hook, None, 0)
    if not hook:
        print('SetWindowsHookEx failed')
        exit(1)
    print('Hook installed, press keys...')

    _GetMessage(byref(wintypes.MSG()), None, 0, 0)
    _UnhookWindowsHookEx(hook)
    print('Hook removed')
