from ctypes import windll, c_int, c_int32, c_voidp, c_void_p, WINFUNCTYPE, wintypes, byref, Structure
from threading import get_ident
from signal import signal, SIGINT

WH_MOUSE_LL = 14
WM_LBUTTONDOWN = 0x0201
WM_LBUTTONUP = 0x0202
WM_RBUTTONDOWN = 0x0204
WM_RBUTTONUP = 0x0205
WM_MBUTTONDOWN = 0x0207
WM_MBUTTONUP = 0x0208
WM_MOUSEMOVE = 0x0200
WM_MOUSEWHEEL = 0x020A
WM_MOUSEHWHEEL = 0x020E
WM_QUIT = 0x0012


class MSLLHOOKSTRUCT(Structure):
    _fields_ = [
        ('pt', wintypes.POINT),
        ('mouseData', wintypes.DWORD),
        ('flags', wintypes.DWORD),
        ('time', wintypes.DWORD),
        ('dwExtraInfo', c_void_p)
    ]


MOUSE_EVENT_MAP = {
    WM_LBUTTONDOWN: 'Left Button Down',
    WM_LBUTTONUP: 'Left Button Up',
    WM_RBUTTONDOWN: 'Right Button Down',
    WM_RBUTTONUP: 'Right Button Up',
    WM_MBUTTONDOWN: 'Middle Button Down',
    WM_MBUTTONUP: 'Middle Button Up',
    WM_MOUSEMOVE: 'Mouse Move',
    WM_MOUSEWHEEL: 'Mouse Wheel',
    WM_MOUSEHWHEEL: 'Mouse Horizontal Wheel'
}

_HOOKPROC = WINFUNCTYPE(
    wintypes.LPARAM,
    c_int32,
    wintypes.WPARAM,
    wintypes.LPARAM
)
_SetWindowsHookEx = windll.user32.SetWindowsHookExW
_SetWindowsHookEx.argtypes = (
    c_int,
    _HOOKPROC,
    wintypes.HINSTANCE,
    wintypes.DWORD
)
_UnhookWindowsHookEx = windll.user32.UnhookWindowsHookEx
_UnhookWindowsHookEx.argtypes = (
    wintypes.HHOOK,
)
_CallNextHookEx = windll.user32.CallNextHookEx
_CallNextHookEx.argtypes = (
    wintypes.HHOOK,
    c_int,
    wintypes.WPARAM,
    wintypes.LPARAM
)
_GetMessage = windll.user32.GetMessageW
_GetMessage.argtypes = (
    c_voidp,
    wintypes.HWND,
    wintypes.UINT,
    wintypes.UINT
)
_PostThreadMessage = windll.user32.PostThreadMessageW
_PostThreadMessage.argtypes = (
    wintypes.DWORD,
    wintypes.UINT,
    wintypes.WPARAM,
    wintypes.LPARAM
)


@_HOOKPROC
def mouse_hook(nCode, wParam, lParam):
    if nCode >= 0:
        mouse_struct = MSLLHOOKSTRUCT.from_address(lParam)
        x, y = mouse_struct.pt.x, mouse_struct.pt.y
        event_name = MOUSE_EVENT_MAP.get(wParam, f'Unknown(0x{wParam:04X})')

        if wParam == WM_MOUSEMOVE:
            print(f'Mouse Move: ({x}, {y})')
        elif wParam in (WM_MOUSEWHEEL, WM_MOUSEHWHEEL):
            wheel_delta = wintypes.SHORT(mouse_struct.mouseData >> 16).value
            direction = 'Up' if wheel_delta > 0 else 'Down'
            wheel_type = 'Vertical' if wParam == WM_MOUSEWHEEL else 'Horizontal'
            print(f'{wheel_type} Wheel {direction}: ({x}, {y}), Delta: {wheel_delta}')
        else:
            print(f'{event_name}: ({x}, {y})')

    return _CallNextHookEx(0, nCode, wParam, lParam)


def signal_handler(signum, _):
    if signum == SIGINT:
        _PostThreadMessage(get_ident(), WM_QUIT, 0, 0)


if __name__ == '__main__':
    signal(SIGINT, signal_handler)
    hook = _SetWindowsHookEx(WH_MOUSE_LL, mouse_hook, None, 0)
    if not hook:
        print('SetWindowsHookEx failed')
        exit(1)
    print('Mouse hook installed, move mouse and click buttons...')

    _GetMessage(byref(wintypes.MSG()), None, 0, 0)
    _UnhookWindowsHookEx(hook)
    print('Hook removed')
